name: 构建并部署到 Netlify

on:
  push:
    branches:
      - 'main'
  workflow_dispatch:

env:
  STORE_PATH: ''

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Ubuntu 构建和推送
    runs-on: ubuntu-latest
    environment:
      name: 正式 Production
      url: https://nolebase.ayaka.io
    steps:
      - name: 签出代码
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 安装 Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: 20.x

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          run_install: false
          version: 8

      - name: 获取 pnpm store 目录
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: 配置 pnpm 缓存
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 构建
        run: pnpm docs:build

      - name: 安装 Netlify CLI
        run: pnpm install -g netlify-cli

      - name: 推送到 Netlify
        timeout-minutes: 10
        run: netlify deploy --dir .vitepress/dist --prod
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

