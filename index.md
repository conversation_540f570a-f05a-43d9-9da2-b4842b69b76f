---
layout: home
sidebar: false

title: xhy
titleTemplate: 记录回忆，知识和畅想的地方

hero:
  name: g~Nj$3J2^
  text: 记录回忆，知识和畅想的地方
  tagline: 以 xhy 为名，读作 nole-base，取自意为「知识」的昆雅语 nólë 和意为「基础」的英文 base，即「知识库」
  image:
    src: /logo.svg
    alt: Vitest
  actions:
    - theme: brand
      text: 开始阅读
      link: /笔记/index
    - theme: alt
      text: 加入 Discord 服务器
      link: https://discord.gg/XuNFDcDZGj
    - theme: alt
      text: GitHub 上浏览
      link: https://github.com/nolebase/nolebase

features:
  - title: 多样的主题和内容
    details: 本知识库和所生成的页面均由创作者们维护，涉及到生活中各方面知识和内容，也不乏我们的回忆和畅想。
    icon: 🌈
  - title: 皆为 Markdown
    details: 使用 Markdown 和 Markdown 拓展语法编写和记录笔记，每一个页面都是 Markdown 文件。
    icon: 📃
  - title: 由 VitePress 驱动
    details: 基于 Vite 的强大静态文档页面生成器，它生成了我们知识库的页面，提供了简单易用的主题和工具。
    icon: 🚀
  - title: 由 Obsidian 驱动
    details: 强大的知识库管理工具，支持花样繁多的插件和拓展，让知识管理变得更加简单。
    icon: 🗃
---

<HomePage />
