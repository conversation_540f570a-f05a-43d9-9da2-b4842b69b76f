{
  // Example markdownlint <PERSON>(C) configuration with all properties set to their default value
  // Default state for all rules
  "default": true,

  // Path to configuration file to extend
  "extends": null,

  // MD001/heading-increment/header-increment - Heading levels should only increment by one level at a time
  "MD001": false,

  // MD013/line-length - Line length
  "MD013": false,

  "MD024": false,

  // MD041/first-line-heading/first-line-h1 - First line in a file should be a top-level heading
  "MD041": false,

  // MD033/no-inline-html - Inline HTML
  "MD033": false,

  // MD045/no-bare-urls - Images should have alternate text (alt text)
  "MD045": false,

  // MD010/no-hard-tabs - Hard tabs
  "MD010": false,

  // MD029/ol-prefix - Ordered list item prefix
  "MD029": false,

  // MD007/ul-indent - Unordered list indentation
  "MD007": false,

  "MD028": false
}
