{"name": "nolebase", "type": "module", "version": "1.0.0", "author": {"name": "xhy", "email": "<EMAIL>", "url": "https://github.com/nolebase"}, "license": "MIT", "keywords": ["vitepress", "nolebase", "markdown", "nolebase-integration", "obsidian", "knowledge-base", "vitepress-doc"], "scripts": {"dev": "pnpm run docs:dev", "build": "pnpm run docs:build", "serve": "pnpm run docs:serve", "docs:dev": "pnpm run update && vitepress dev", "docs:build": "pnpm run update && vitepress build", "docs:serve": "vitepress serve", "update": "tsx scripts/update.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@antfu/eslint-config": "^2.12.2", "@iconify-json/eos-icons": "^1.1.10", "@iconify-json/ic": "^1.1.17", "@iconify-json/octicon": "^1.1.53", "@iconify-json/svg-spinners": "^1.1.2", "@netlify/functions": "^1.6.0", "@nolebase/markdown-it-bi-directional-links": "2.0.0-rc11", "@nolebase/markdown-it-unlazy-img": "2.0.0-rc11", "@nolebase/vitepress-plugin-enhanced-mark": "2.0.0-rc11", "@nolebase/vitepress-plugin-enhanced-readabilities": "2.0.0-rc11", "@nolebase/vitepress-plugin-git-changelog": "2.0.0-rc11", "@nolebase/vitepress-plugin-highlight-targeted-heading": "2.0.0-rc11", "@nolebase/vitepress-plugin-inline-link-preview": "2.0.0-rc11", "@nolebase/vitepress-plugin-og-image": "2.0.0-rc11", "@nolebase/vitepress-plugin-page-properties": "2.0.0-rc11", "@nolebase/vitepress-plugin-thumbnail-hash": "2.0.0-rc11", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.0", "@types/markdown-it": "^12.2.3", "@types/markdown-it-footnote": "^3.0.4", "@types/node-fetch": "^2.6.11", "@unocss/eslint-config": "^0.58.9", "@unocss/reset": "^0.58.9", "@vueuse/core": "^10.9.0", "@vueuse/shared": "^10.9.0", "emoji-regex": "^10.3.0", "eslint": "^8.57.0", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "gray-matter": "^4.0.3", "less": "^4.2.0", "lodash": "^4.17.21", "markdown-it": "^13.0.2", "markdown-it-footnote": "^3.0.3", "markdown-it-mathjax3": "^4.3.2", "node-fetch": "^3.3.2", "sharp": "^0.32.6", "simple-git": "^3.24.0", "tsx": "^4.7.2", "typescript": "^5.4.4", "unocss": "^0.58.9", "unplugin-vue-components": "^0.26.0", "uuid": "^9.0.1", "vite": "^5.2.8", "vite-plugin-inspect": "^0.8.3", "vitepress": "^1.0.2", "vitepress-plugin-comment-with-giscus": "^1.1.15", "vue": "^3.4.21"}}